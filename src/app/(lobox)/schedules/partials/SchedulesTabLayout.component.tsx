'use client';

import dayjs from 'dayjs';
import dynamic from 'next/dynamic';
import { useRouter, useSearchParams } from 'next/navigation';
import { type PropsWithChildren, useCallback } from 'react';
import { ProviderType } from '@shared/components/molecules/EventsIntegration/utils/type';
import ResponsiveGlobalSearch from '@shared/components/molecules/ResponsiveGlobalSearch';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import {
  selectDisplayDate,
  selectViewDate,
} from '@shared/stores/schedulesStore/schedulesStore.selectors';
import { type TabType } from '@shared/uikit/Tabs';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import formatDate from '@shared/utils/toolkit/formatDate';
import JobsTabLayout from 'shared/components/layouts/JobsLayouts/JobsTabLayout/JobTabLayout.component';
import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import Button from 'shared/uikit/Button';
import Media from 'shared/uikit/Media';
import cnj from 'shared/uikit/utils/cnj';
import useMedia from 'shared/uikit/utils/useMedia';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import { routeNames } from 'shared/utils/constants/routeNames';
import {
  setDate as setViewDate,
  useSchedulesState,
} from 'shared/stores/schedulesStore';
import useLocation from 'shared/utils/hooks/useLocation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import { EventsIntegrationWithModal } from 'shared/components/molecules/EventsIntegration/EventsIntegration.modal';
import { useEventIntegrations } from 'shared/components/molecules/EventsIntegration/utils/useEventIntegrations';
import classes from './SchedulesTabLayout.component.module.scss';

const IntegrationsPanel = dynamic(
  () => import('./SchedulesTabLayout/IntegrationsPanel'),
  { ssr: false }
);

const AvailabilityModule = dynamic(
  () =>
    import('shared/components/Organism/AvailabilityModule/AvailabilityModule'),
  { ssr: false }
);

const SchedulesTabLayout = ({ children }: PropsWithChildren) => {
  const { t } = useTranslation();
  const { isTabletAndLess } = useMedia();
  const location = useLocation();
  const pathName = location.pathname;
  const isLoggedIn = useAuthState('isLoggedIn');
  const { isIntegrationPanelOpen, toggleIntegrationPanel } =
    useEventIntegrations();

  const {
    state: { scheduleAvailabilityPanelData, scheduleEventsPanelData },
    setScheduleAvailabilityPanelData,
    closeIntegrationsPanel,
  } = useSchedulesUrlState();

  const router = useRouter();

  const isHeaderSearchOpen = useAuthState('isHeaderSearchOpen');

  const isCalendarRoute = pathName?.startsWith(routeNames.schedulesCalendar);
  const isCreateScheduleRoute = pathName?.startsWith(routeNames.createSchedule);
  const isCalendar = pathName?.includes('calendar');
  const isAvailabilityRoute =
    pathName === routeNames.schedulesAvailability.main;

  const isInAvailabilityRoute = pathName?.includes(
    routeNames.schedulesAvailability.main
  );
  const isCrEditAvailabilityRoute =
    pathName === routeNames.schedulesAvailabilityCrEdit.main;
  const isInCrEditAvailabilityRoute = pathName?.includes(
    routeNames.schedulesAvailabilityCrEdit.main
  );
  const isInAvailableHoursRoute = pathName?.includes(
    routeNames.schedulesAvailabilityDay.main
  );
  const hideLinksOnMobile =
    isCalendarRoute || isCreateScheduleRoute || isInAvailabilityRoute;
  const { calendarBoardType, openCreateEventWithDate } = useSchedulesCalendar();

  const { start: viewingStartDate } = useSchedulesState(selectViewDate);
  const displayDate = useSchedulesState(selectDisplayDate);

  const calendarLabel =
    calendarBoardType === 'year'
      ? formatDate(displayDate, 'YYYY')
      : calendarBoardType === 'day'
        ? formatDate(displayDate, 'LL')
        : formatDate(displayDate, 'MMMM, YYYY');
  const headerTitle = isAvailabilityRoute
    ? t('availabilities')
    : isInAvailabilityRoute
      ? isCrEditAvailabilityRoute
        ? t('add_availability')
        : isInCrEditAvailabilityRoute
          ? t('edit_availability')
          : isInAvailableHoursRoute
            ? t('available_hours')
            : t('availability')
      : isCalendarRoute
        ? calendarLabel
        : isCreateScheduleRoute
          ? t('create_schedule')
          : t('events');

  const onCreateScheduleClick = () => {
    const schedulesEventType = pathName.includes(routeNames.schedulesTasks)
      ? ScheduleEventTypes.TASK
      : pathName.includes(routeNames.schedulesReminders)
        ? ScheduleEventTypes.REMINDER
        : ScheduleEventTypes.MEETING;
    const creationDateTime = !isCalendarRoute
      ? undefined
      : dayjs().isSame(viewingStartDate, 'day')
        ? undefined
        : viewingStartDate.set('hour', 8).set('minute', 0);
    openCreateEventWithDate(creationDateTime, { schedulesEventType });
  };
  const onAvailabilityButtonClick = () => {
    setScheduleAvailabilityPanelData({
      ...scheduleAvailabilityPanelData,
      isInAvailabilities: true,
    });
  };

  const tabs: TabType[] = [
    !isTabletAndLess && {
      path: routeNames.schedulesCalendar,
      defaultPath: 'month',
      title: t('calendar'),
      shallow: true,
    },
    {
      path: routeNames.schedulesMeetings,
      title: t('meetings'),
      shallow: true,
    },
    {
      path: routeNames.schedulesEvents,
      title: t('events'),
      shallow: true,
    },
    {
      path: routeNames.schedulesReminders,
      title: t('reminders'),
      shallow: true,
    },
    {
      path: routeNames.schedulesTasks,
      title: t('tasks'),
      shallow: true,
    },
  ].filter((item) => !!item);

  const searchParams = useSearchParams();
  const shouldGoBackToCalendar =
    !searchParams.has('state') &&
    [
      routeNames.schedulesEvents,
      routeNames.schedulesMeetings,
      routeNames.schedulesReminders,
      routeNames.schedulesTasks,
    ].includes(location.pathname);

  const backHandler = () => {
    if (shouldGoBackToCalendar) router.push(routeNames.schedulesCalendar);
    else router.back();
  };

  const gotoToday = () => {
    setViewDate(dayjs());
  };

  const jobsTabRenderContent = useCallback(
    () =>
      isInAvailabilityRoute ? null : isCalendarRoute ? (
        <>
          <Button
            schema="semi-transparent2"
            labelColor="brand"
            onClick={gotoToday}
            className={cnj(classes.todayButton, classes.firstButton)}
            label={t('today_cap')}
          />
          <IconButton
            name="sync2"
            colorSchema="transparent1"
            type="far"
            onClick={toggleIntegrationPanel}
            className={classes.syncIcon}
          />
        </>
      ) : (
        <>
          <IconButton
            name="sync2"
            colorSchema="transparent1"
            type="far"
            onClick={toggleIntegrationPanel}
            className={cnj(classes.syncIcon, classes.firstButton)}
          />
          <ResponsiveGlobalSearch isFlexOne={false} />
        </>
      ),
    [isInAvailabilityRoute, isCalendarRoute, t, toggleIntegrationPanel]
  );

  if (!isLoggedIn && isInAvailabilityRoute) {
    return children;
  }

  return (
    <>
      <JobsTabLayout
        isAvailabilityMobileView
        tabs={tabs}
        tabsRootWrapperClassName={classes.tabsRootWrapperClassName}
        tabsInnerClassName={cnj(
          isCalendar && classes.tabsInnerWrapperCalendar,
          isCalendar && isHeaderSearchOpen && classes.isHeaderSearchOpen,
          !isLoggedIn && classes.contentRootUnAuthClassName
        )}
        contentRootClassName={cnj(
          isCalendar
            ? classes.contentRootClassNameCalendar
            : classes.contentRootClassName,
          isCalendar && isHeaderSearchOpen && classes.isHeaderSearchOpen,
          !isLoggedIn && classes.contentRootUnAuthClassName,
          isInAvailabilityRoute && classes.background
        )}
        hasScrollView
        linksRootClassName={cnj(
          hideLinksOnMobile && classes.linksRootClassName,
          !isLoggedIn && classes.displayNone
        )}
        actionButton={
          <Media greaterThan="tablet">
            <Flex flexDir="row">
              <Button
                leftIcon="calendar-timer-light"
                leftType="far"
                label={t('availability')}
                className={classes.availabilityBtn}
                onClick={onAvailabilityButtonClick}
                schema="semi-transparent"
              />
              <Button
                leftIcon="plus"
                leftType="fas"
                label={t('create')}
                className={classes.createBtn}
                onClick={onCreateScheduleClick}
              />
            </Flex>
          </Media>
        }
        headerProps={{
          backButtonProps: {
            onClick: backHandler,
            className:
              isHeaderSearchOpen || isCalendarRoute
                ? classes.hideBackButton
                : undefined,
          },
          visibleHeaderDivider: true,
          title: isHeaderSearchOpen ? undefined : headerTitle,
          rightContent: jobsTabRenderContent,
        }}
      >
        {children}
      </JobsTabLayout>
      {!isInAvailabilityRoute && <AvailabilityModule />}
      {scheduleEventsPanelData?.isInIntegrations && (
        <IntegrationsPanel
          handleClose={closeIntegrationsPanel}
          providerType={ProviderType.Conference}
        />
      )}
      {isIntegrationPanelOpen && isTabletAndLess && (
        <EventsIntegrationWithModal />
      )}
    </>
  );
};

export default SchedulesTabLayout;
