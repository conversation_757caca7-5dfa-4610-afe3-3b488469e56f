import React from 'react';
import LocationListWithGoogleMapProps from '@shared/components/Organism/LocationListWithGoogleMapProps';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { CompanyType } from '@shared/types/company';

interface Props {
  company: CompanyType;
}

const Location: React.FC<Props> = ({ company }) => {
  const { t } = useTranslation();
  const locations = company?.locations || [];

  if (!locations || locations.length === 0) {
    return null;
  }

  return (
    <SectionLayout
      title={t('locations')}
      classNames={{ childrenWrap: '!p-0' }}
      visibleActionButton={false}
    >
      <LocationListWithGoogleMapProps locations={locations} disabled={false} />
    </SectionLayout>
  );
};

export default Location;
