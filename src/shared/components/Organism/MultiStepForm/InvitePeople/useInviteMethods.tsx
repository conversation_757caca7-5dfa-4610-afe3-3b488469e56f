import { type Dispatch, type SetStateAction, useMemo, useState } from 'react';
import QRCode from 'react-qr-code';
import useBackToModal from '@shared/hooks/useBackToModal';
import { closeMultiStepForm } from '@shared/hooks/useMultiStepForm';
import { InvitationEntityType } from '@shared/types/invitation';
import { HorizontalDividerWithLabel } from 'shared/components/molecules/HorizontalDividerWithLabel';
import EmailIcon2 from 'shared/svg/landing/EmailIcon2';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import GoogleIcon from 'shared/uikit/shared/svg/GoogleIcon';
import Typography from 'shared/uikit/Typography';
import copyToClipboard from 'shared/uikit/utils/copyToClipboard';
import useTheme from 'shared/uikit/utils/useTheme';
import { getReferralCode } from 'shared/utils/api/invite';
import { landingRouteNames, QueryKeys } from 'shared/utils/constants';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import classes from './useInviteMethods.module.scss';
import type { MultiStepFormProps } from '../MultiStepForm';
import type { Method } from '@shared/components/Organism/MultiStepForm/InvitePeople/types';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
};

const qrCodeSize = 164;

type Args = {
  setMethod: Dispatch<SetStateAction<Method>>;
  entity?: InvitationEntityType;
};

export function useInviteMethods({
  setMethod,
  entity = InvitationEntityType.PEOPLE,
}: Args): SingleDataItem[] {
  const { t } = useTranslation();
  const [isLinkCopiedRecently, setIsLinkCopiedRecently] = useState(false);
  const { isDark } = useTheme();
  const { hasBackModal, backToModal } = useBackToModal('createEntityPanel');
  const { data: referralCode } = useReactQuery<string>({
    action: {
      key: [QueryKeys.getReferralCode],
      apiFunc: getReferralCode,
    },
  });

  const invitationLink = useMemo(() => {
    const url = new URL(`${window.location.origin}${landingRouteNames.signup}`);
    url.searchParams.set('referralCode', referralCode || '');

    return url?.toString();
  }, [referralCode]);

  const handleCopyLink = () => {
    copyToClipboard(invitationLink);
    setIsLinkCopiedRecently(true);
    setTimeout(() => setIsLinkCopiedRecently(false), 2000);
  };

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title:
      entity === InvitationEntityType.COMPANY
        ? t('invite_company')
        : t('invite_people'),
    hideBack: !hasBackModal,
    noCloseButton: hasBackModal,
    backButtonProps: {
      onClick: () => {
        if (entity === InvitationEntityType.PEOPLE) {
          backToModal();
        }
        closeMultiStepForm('invitePeople');
        event.trigger(eventKeys.closeModal);
      },
    },
  });

  const data: Array<SingleDataItem> = [
    {
      stepKey: 'methods',
      getHeaderProps,
      renderBody: ({ setStep, values }) => (
        <Flex className={classes.bodyWrapper}>
          <Flex>
            <Typography
              font="700"
              size={20}
              height={28}
              color="smoke_coal"
              mb={4}
            >
              {entity === InvitationEntityType.COMPANY
                ? t('build_lasting_b_c_w_l')
                : t('thrive_friendship_with_lobox_invites')}
            </Typography>
            <Typography size={15} height={21} color="secondaryDisabledText">
              {entity === InvitationEntityType.COMPANY
                ? t('y_can_con_with_t_b_y_i_oc_th_j')
                : t('you_can_control_who_to_connect_and_concatcs_anytime')}
            </Typography>
          </Flex>
          {entity === InvitationEntityType.PEOPLE && (
            <>
              <Button
                leftSvg={<GoogleIcon className={classes.linkedInIcon} />}
                className={classes.methodButton}
                labelClassName={classes.methodButtonLabel}
                schema="dark-primary"
                label={t('sync_with_google')}
                variant="large"
                labelFont="400"
                onClick={() => {
                  setMethod('google');
                  setStep((prev) => prev + 1);
                }}
              />
              <HorizontalDividerWithLabel
                label={t('or_lower')}
                classNames={{ wrapper: classes.noMargin }}
              />
            </>
          )}
          <Button
            leftSvg={<EmailIcon2 className={classes.linkedInIcon} />}
            className={classes.methodButton}
            labelClassName={classes.methodButtonLabel}
            schema="dark-primary"
            label={t('email')}
            variant="large"
            labelFont="400"
            onClick={() => {
              setMethod('emails');
              setStep((prev) => prev + 1);
            }}
          />
          <HorizontalDividerWithLabel
            label={t('or_lower')}
            classNames={{ wrapper: classes.noMargin }}
          />
          <Flex>
            <Typography
              font="700"
              size={20}
              height={28}
              color="smoke_coal"
              mb={4}
            >
              {t('use_invitation_link_or_qr_for_faster')}
            </Typography>
            <Typography size={15} height={21} color="secondaryDisabledText">
              {t('provide_your_uniqe_link_can_be_easily_shared_audience')}
            </Typography>
          </Flex>
          <Flex className={classes.qrWrapper}>
            <QRCode
              viewBox={`0 0 ${qrCodeSize} ${qrCodeSize}`}
              size={qrCodeSize}
              value={invitationLink}
              style={{ height: 'auto', maxWidth: '70%', width: 190 }}
              bgColor={isDark ? '#181818' : '#f3f5f7'}
              fgColor={isDark ? '#ffffff' : '#000000'}
            />
            <Flex className={classes.copyLinkbutton}>
              {!isLinkCopiedRecently ? (
                <Button
                  leftIcon="link"
                  leftType="far"
                  schema="semi-transparent"
                  label={t('copy_invitation_link')}
                  labelSize={15}
                  onClick={handleCopyLink}
                />
              ) : (
                <Button
                  key={t('link_copied')}
                  leftIcon="check-circle"
                  leftType="fas"
                  schema="success-semi-transparent"
                  label={t('link_copied')}
                  labelSize={15}
                />
              )}
            </Flex>
          </Flex>
        </Flex>
      ),
    },
  ];

  return data;
}
