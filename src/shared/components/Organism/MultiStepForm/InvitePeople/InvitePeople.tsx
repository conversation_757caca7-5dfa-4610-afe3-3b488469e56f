'use client';

import React, { useState } from 'react';
import { InvitationEntityType } from '@shared/types/invitation';
import { invitationEndpoints } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import useToast from 'shared/uikit/Toast/useToast';
import getStepData from 'shared/utils/getStepData';
import MultiStepForm from '../MultiStepForm';
import { useEmail } from './useEmail';
import { useGoogle } from './useGoogle';
import { useInviteMethods } from './useInviteMethods';
import { useSendStep } from './useSendStep';
import type { MultiStepFormProps } from '../MultiStepForm';

type Props = {};

const InvitiePeopleMultiStepForm: React.FC<Props> = () => {
  const { data: invitePeopleData } = useMultiStepFormState('invitePeople');
  const [method, setMethod] = useState<Method>(invitePeopleData?.initialMethod);
  const initialStep = invitePeopleData?.initialMethod === 'google' ? 1 : 0;
  const [sendMethod, setSendMethod] = useState<
    'user' | 'anonymous' | (string & {})
  >('user');

  const data = useInviteMethods({
    setMethod,
    entity: invitePeopleData?.entity,
  });
  const sendStep = useSendStep({ method, sendMethod, setSendMethod });
  const googleData = useGoogle();
  const emailData = useEmail({ setMethod });
  const combinedData = [
    ...data,
    ...(method === 'google' ? googleData : []),
    ...(['emails', 'bulk'].includes(method as any) ? emailData : []),
    ...sendStep,
  ];
  const toast = useToast();
  const { t } = useTranslation();

  const onClose = () => closeMultiStepForm('invitePeople');
  const onSuccess = () => {
    closeMultiStepForm('invitePeople');
    toast({
      type: 'success',
      icon: 'check-circle',
      title: t('invitation_completed'),
      message: t(
        method === 'google'
          ? 'invitation_requests_to_google_contacts_have_been_sent'
          : 'invitation_requests_to_email_contacts_have_been_sent'
      ),
    });
  };

  const initialValues: MultiStepFormProps['initialValues'] = {};

  // No need to change these
  const totalSteps: MultiStepFormProps['totalSteps'] = combinedData?.length;
  const getHeaderProps = getStepData('getHeaderProps', combinedData);
  const getStepHeaderProps = getStepData('getStepHeaderProps', combinedData);
  const renderBody = getStepData('renderBody', combinedData);
  const renderFooter = getStepData('renderFooter', combinedData);

  const sendingProps = {
    url:
      method === 'bulk'
        ? invitationEndpoints.confirmBulkInvitations
        : invitationEndpoints.sendUserInvitations,
    method: 'POST' as const,
  };

  const transform = (values: { emails: string[] }) => ({
    sender: sendMethod === 'user',
    emails:
      method !== 'bulk'
        ? values?.emails?.map((item) => item?.id || item)
        : undefined,
    pageId:
      !!sendMethod && !['user', 'anonymous']?.includes(sendMethod)
        ? sendMethod
        : undefined,
  });

  return (
    <MultiStepForm
      initialStep={initialStep}
      showConfirm={false}
      totalSteps={totalSteps}
      initialValues={initialValues}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      onSuccess={onSuccess}
      wide={invitePeopleData?.entity === InvitationEntityType.COMPANY}
      transform={transform}
      {...sendingProps}
      formName="invitePeople"
    />
  );
};

export default InvitiePeopleMultiStepForm;
