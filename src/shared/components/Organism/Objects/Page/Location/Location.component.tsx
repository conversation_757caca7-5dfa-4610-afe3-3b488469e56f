import React from 'react';
import LocationListWithGoogleMapProps from '@shared/components/Organism/LocationListWithGoogleMapProps';
import useIsPageEditEnable from 'shared/hooks/useIsPageEditEnable';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import useProfilePage from 'shared/hooks/useProfilePage';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SectionLayout from '../../Common/Section.layout';
import type ILocation from 'shared/utils/ILocation';

const Location: React.FC = () => {
  const { t } = useTranslation();

  const { isAuthBusinessPage, getObjectProp, isPageCreation } =
    useProfilePage();

  const editAble = useIsPageEditEnable();

  const locations = getObjectProp({ pageKey: 'locations' }) as Array<ILocation>;
  const addresses = locations?.length
    ? locations
    : [
        {
          id: 'location',
          name: t('location'),
          subTitle: editAble ? t('add_location') : t('no_location_ent'),
          to: 'settings',
        },
      ];
  const disabled = !editAble && locations?.length === 1;

  const onClickAction = () => {
    if (editAble) {
      openMultiStepForm({
        formName: 'createPageForm',
        stepKey: 'locations',
      });
    }
  };

  if (!locations?.length && !isAuthBusinessPage && !isPageCreation) {
    return false;
  }

  return (
    <SectionLayout
      title={t('locations')}
      classNames={{ childrenWrap: '!p-0' }}
      onEditHandler={onClickAction}
      visibleActionButton={editAble}
    >
      <LocationListWithGoogleMapProps
        locations={addresses}
        disabled={disabled}
        onItemClick={onClickAction}
        // listClassName=" px-16_20"
      />
    </SectionLayout>
  );
};

export default Location;
