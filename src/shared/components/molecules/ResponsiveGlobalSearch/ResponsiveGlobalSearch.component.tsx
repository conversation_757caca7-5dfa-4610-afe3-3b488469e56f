import React from 'react';
import GlobalSearchInputSkeleton from 'shared/components/molecules/GlobalSearchInput/GlobalsSearchInput.skeleton';
import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import useMedia from 'shared/uikit/utils/useMedia';
import ResponsiveGlobalSearchDesktop from './ResponsiveGlobalSearch.desktop';
import ResponsiveGlobalSearchMobile from './ResponsiveGlobalSearch.mobile';

interface Props {
  iconViewClassName?: string;
  defaultView?: 'icon' | 'input' | 'text';
  isFlexOne?: boolean;
}

const ResponsiveGlobalSearch: React.FC<Props> = ({
  iconViewClassName,
  defaultView,
  isFlexOne,
}) => {
  const isLoggedIn = useAuthState('isLoggedIn');
  const { isMoreThanTablet } = useMedia();
  if (!isLoggedIn) return null;

  if (typeof isMoreThanTablet === 'undefined') {
    return <GlobalSearchInputSkeleton />;
  }

  return isMoreThanTablet ? (
    <ResponsiveGlobalSearchDesktop />
  ) : (
    <ResponsiveGlobalSearchMobile
      iconViewClassName={iconViewClassName}
      defaultView={defaultView}
      isFlexOne={isFlexOne}
    />
  );
};

export default ResponsiveGlobalSearch;
