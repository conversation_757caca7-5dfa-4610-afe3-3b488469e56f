import React from 'react';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex/index';
import Tooltip from '@shared/uikit/Tooltip';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import copyToClipboard from '@shared/uikit/utils/copyToClipboard';
import type { JSX } from 'react';

interface Props {
  title: string;
  entityId: string;
  tooltip: string;
  className?: string;
}

const CopyEntityId = ({
  title,
  tooltip,
  entityId,
  className,
}: Props): JSX.Element => {
  const onClickHandler = () => copyToClipboard(entityId);

  return (
    <Flex
      flexDir="row"
      className={cnj(
        'mx-auto mt-24_32  justify-center align-middle items-center',
        className
      )}
    >
      <Typography
        size={14}
        height={18}
        color="primaryDisabledText"
        font="700"
        mr={4}
      >
        {`${title}: ${entityId}`}
      </Typography>
      <Tooltip
        placement="top"
        trigger={
          <IconButton
            colorSchema="secondary-transparent"
            size="stiny"
            type="far"
            name="info-circle"
          />
        }
      >
        <Typography size={13} font="400" height={17} color="tooltipText">
          {tooltip}
        </Typography>
      </Tooltip>
      <IconButton
        className="ml-8"
        variant="rectangle"
        onClick={onClickHandler}
        size="md20"
        type="far"
        name="clone"
        colorSchema="semi-transparent"
      />
    </Flex>
  );
};

export default CopyEntityId;
