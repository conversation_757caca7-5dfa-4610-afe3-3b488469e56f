'use client';

import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import useTranslation from 'shared/utils/hooks/useTranslation';
import {
  useGlobalState,
  useGlobalDispatch,
} from 'shared/contexts/Global/global.provider';
import urls from 'shared/constants/urls';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import { pageStatus, PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import useProfilePage from 'shared/hooks/useProfilePage';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useHasPermission from 'shared/hooks/useHasPermission';
import dynamic from 'next/dynamic';
import type { QueryParamsType } from 'shared/types/queryParams';
import ResponsiveGlobalSearch from 'shared/components/molecules/ResponsiveGlobalSearch';
import { useRouter } from 'next/navigation';
import React, { useRef } from 'react';
import Flex from 'shared/uikit/Flex';
import FooterDesktop from 'shared/uikit/Footer/FooterDesktop';
import Media from 'shared/uikit/Media';
import Tabs from 'shared/uikit/Tabs';
import cnj from 'shared/uikit/utils/cnj';
import { routeNames } from 'shared/utils/constants/routeNames';
import useLocation from 'shared/utils/hooks/useLocation';
import useParams from 'shared/utils/hooks/useParams';
import isRouterAccessibleByPortal from 'shared/utils/isRouterAccessibleByPortal';
import ProfileHeader from '../ProfileLayout.Header';
import classes from './ProfileLayout.Content.module.scss';
import useObjectProfileActions from './useObjectProfileActions';
import type ScrollViewElement from 'shared/utils/ScrollViewElement';

const ProfileHeaderPublishPage = dynamic(
  () => import('../ProfileLayout.Header/partials/ProfileHeader.PublishPage'),
  { ssr: false }
);
const BlockedAccount = dynamic(
  () => import('shared/components/molecules/BlockedAccount/BlockedAccount'),
  { ssr: false }
);

const removeSearchParam = (url, param) => {
  const urlObj = new URL(url);
  urlObj.searchParams.delete(param);

  return urlObj.toString();
};

interface ProfileLayoutContentProps {
  children?: ReactNode;
}

const ProfileLayoutContent: React.FC<ProfileLayoutContentProps> = ({
  children,
}) => {
  const globalDispatch = useGlobalDispatch();
  const isLoggedIn = useAuthState('isLoggedIn');
  const canSeeDashboardTab = useHasPermission([SCOPES.canSeeDashboardTab]);
  const canSeeAboutTab = useHasPermission([SCOPES.canSeeAboutTab]);
  const canSeeFeedTab = useHasPermission([SCOPES.canSeeFeedTab]);
  const canSeePagesTab = useHasPermission([SCOPES.canSeePagesTab]);
  const canSeeCollectionsTab = useHasPermission([SCOPES.canSeeCollectionsTab]);
  const canSeeHashtagTab = useHasPermission([SCOPES.canSeeHashtagTab]);
  const canSeeJobsTab = useHasPermission([SCOPES.canSeeJobsTab]);
  const canSeePeopleTab = useHasPermission([SCOPES.canSeePeopleTab]);

  const location = useLocation();
  const { pathname } = location;
  const { username } = useParams<QueryParamsType>();
  const { t } = useTranslation();
  const scrollRef = useRef<ScrollViewElement>(null);
  const headerRef = useRef<HTMLElement>(null);
  const profileHistoryCount = useGlobalState('profileHistoryCount');

  const {
    isBusinessApp,
    isAuthUser,
    isPageCreation,
    objectDetail,
    isPage,
    isAuthBusinessPage,
    isPrivateAccount,
  } = useProfilePage();

  const blockedAccount =
    objectDetail?.youHaveBlocked || objectDetail?.youAreBlocked;

  const visibleFooter = false;
  const showPublishPageButton = objectDetail?.status === pageStatus.UNPUBLISHED;
  const visibleAboutTab = canSeeAboutTab;
  const visibleHashtagTab =
    isLoggedIn &&
    !isPrivateAccount &&
    canSeeHashtagTab &&
    (isAuthUser ||
      isAuthBusinessPage ||
      isPageCreation ||
      objectDetail?.network?.hasAnyHashtagFollowings);
  const visibleFeedTab = isLoggedIn && canSeeFeedTab && !isPrivateAccount;
  const visiblePagesTab =
    isLoggedIn &&
    !isPrivateAccount &&
    canSeePagesTab &&
    (isAuthUser ||
      isAuthBusinessPage ||
      isPageCreation ||
      objectDetail?.network?.hasAnyPageFollowings);
  const visiblePeople =
    isLoggedIn &&
    isPage &&
    ((isBusinessApp && canSeePeopleTab) || !isBusinessApp || isPageCreation);
  const isVisibleJobsTab =
    isLoggedIn &&
    canSeeJobsTab &&
    (isAuthUser || isPage || isPageCreation) &&
    isRouterAccessibleByPortal(routeNames.jobs);

  const isVisibleSchedulesTab =
    isLoggedIn && !isPrivateAccount && (isAuthUser || isPageCreation);

  const visibleDashboardTab =
    isLoggedIn &&
    !isPrivateAccount &&
    canSeeDashboardTab &&
    (isAuthUser || isPageCreation || (isAuthBusinessPage && isBusinessApp));

  const isVisibleCollectionsTab =
    isLoggedIn &&
    !isPrivateAccount &&
    canSeeCollectionsTab &&
    (isAuthUser || isAuthBusinessPage || objectDetail?.hasAnyPublicCollection);

  const tabs = blockedAccount
    ? []
    : [
        {
          path: isPageCreation ? routeNames.pageCreation : `/${username}`,
          title: t('about'),
          exact: true,
          hide: !visibleAboutTab,
        },
        {
          path: `/${username}${routeNames.feed}`,
          title: t('feed'),
          disabled: isPageCreation,
          hide: !visibleFeedTab,
        },
        {
          path: `/${username}${routeNames.profilePages}`,
          title: t('pages'),
          disabled: isPageCreation,
          hide: !visiblePagesTab,
        },
        {
          path: `/${username}${routeNames.hashtags}`,
          title: t('hashtags'),
          disabled: isPageCreation,
          hide: !visibleHashtagTab,
        },
        {
          path: `/${username}${routeNames.collections}`,
          title: t('collections'),
          disabled: isPageCreation,
          hide: !isVisibleCollectionsTab,
        },
        {
          path: `/${username}${routeNames.people}`,
          title: t('people'),
          disabled: isPageCreation,
          hide: !visiblePeople,
        },
        {
          path: `/${username}${routeNames.jobs}`,
          title: t('jobs'),
          disabled: isPageCreation,
          hide: !isVisibleJobsTab,
        },
        {
          path: `/${username}${routeNames.schedules}`,
          title: t('schedules'),
          disabled: isPageCreation,
          hide: !isVisibleSchedulesTab,
        },
        {
          path: `/${username}${routeNames.dashboard}`,
          title: t('insights'),
          disabled: isPageCreation,
          hide: !visibleDashboardTab,
        },
        {
          path: `/${username}${routeNames.availability.week}`,
          title: t('availability'),
          disabled: isPageCreation,
          hide: isAuthUser || isPage || isPageCreation,
        },
      ].filter(({ hide }) => !hide);

  const router = useRouter();

  React.useEffect(() => {
    const currentUrl = window.location.href;
    const newUrl = removeSearchParam(currentUrl, 'objectTempId');

    if (currentUrl !== newUrl) {
      router.replace(newUrl);
    }
  }, [router]);

  const onBackHandler = () => {
    if (profileHistoryCount > 0) {
      window.history.go(profileHistoryCount * -1);
      globalDispatch({ type: 'RESET_PROFILE_HISTORY_COUNT' });

      return;
    }
    window.history.back();
  };

  const profileLayoutActions = useObjectProfileActions({});
  const visibleActions = !Array.isArray(profileLayoutActions);

  return (
    <>
      <ModalHeaderSimple
        visibleHeaderDivider
        className={classes.mobileHeader}
        title={isPage ? t('page_details') : t('user_details')}
        rightContent={() => <ResponsiveGlobalSearch />}
        backButtonProps={{ onClick: onBackHandler }}
      />
      <Flex
        id={PROFILE_SCROLL_WRAPPER}
        className={classes.profileRoot}
        ref={scrollRef}
      >
        {showPublishPageButton && <ProfileHeaderPublishPage />}
        <ProfileHeader
          headerRef={headerRef}
          actionButtons={
            visibleActions && (
              <Flex
                className={cnj(
                  classes.tabBarAddSectionBtn,
                  isPrivateAccount && classes.paddingBottom8
                )}
              >
                {profileLayoutActions}
              </Flex>
            )
          }
          blockedAccount={blockedAccount as boolean}
          isPrivateAccount={isPrivateAccount}
        />
        <Tabs
          moreLabel={t('more')}
          linksRootClassName={cnj(
            classes.linksRootShrink,
            showPublishPageButton && classes.linksRootUnpublished
          )}
          contentRootClassName={cnj(
            classes.contentRootClassName,
            blockedAccount && classes.fullHeight
          )}
          className={cnj(
            classes.tabsRootWrapper,
            blockedAccount && classes.flexOne
          )}
          innerClassName={cnj(isPageCreation && classes.disabledContentClick)}
          navLinkClassName={classes.navLinkClassName}
          linkAndActionWrapperClassName={cnj(
            classes.linkAndActionWrapperClassName,
            classes.borderTop
          )}
          tabs={tabs}
          tabsShowLimit={9}
          actionButton={
            visibleActions && (
              <PermissionsGate
                scopes={[SCOPES.canSeeProfileHeaderRightActions]}
              >
                <Media greaterThan="tablet">
                  <Flex className={classes.tabBarAddSectionBtn}>
                    {profileLayoutActions}
                  </Flex>
                </Media>
              </PermissionsGate>
            )
          }
        >
          {objectDetail?.youHaveBlocked ? (
            <BlockedAccount isPage={isPage} />
          ) : (
            <>{children}</>
          )}
        </Tabs>
        {visibleFooter && (
          <Media greaterThan="tablet">
            <FooterDesktop baseUrl={urls.base} logoGray />
          </Media>
        )}
      </Flex>
    </>
  );
};

export default ProfileLayoutContent;
