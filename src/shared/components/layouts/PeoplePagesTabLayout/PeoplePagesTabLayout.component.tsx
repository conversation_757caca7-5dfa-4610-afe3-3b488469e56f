'use client';

import React, { useEffect, useMemo } from 'react';
import useInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import ResponsiveGlobalSearch from 'shared/components/molecules/ResponsiveGlobalSearch';
import { PROFILE_SCROLL_WRAPPER } from 'shared/constants/enums';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import useHasPermission from 'shared/hooks/useHasPermission';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import useNavigateToCreatePage from 'shared/hooks/useNavigateToCreatePage';
import { useNavigateToMainPageOfSection } from 'shared/hooks/useNavigateToMainPageOfSection';
import Button from 'shared/uikit/Button';
import useMedia from 'shared/uikit/utils/useMedia';
import {
  getMyFollowRequests,
  getPendingRequests,
} from 'shared/utils/api/network/people';
import QueryKeys from 'shared/utils/constants/queryKeys';
import { mainRoutes, routeNames } from 'shared/utils/constants/routeNames';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import useLocation from 'shared/utils/hooks/useLocation';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import JobsTabLayout from '../JobsLayouts/JobsTabLayout/JobTabLayout.component';
import classes from './PeoplePagesTabLayout.component.module.scss';
import type { BlockGen } from '@shared/types';
import type { PeopleType } from '@shared/types/people';

interface PeoplePagesTabLayoutProps {
  children?: ReactNode;
  type?: 'people' | 'pages';
}

const PeoplePagesTabLayout: React.FC<PeoplePagesTabLayoutProps> = ({
  children,
  type,
}) => {
  const { t } = useTranslation();
  const { pathname, path } = useLocation();

  const { isMoreThanTablet } = useMedia();

  const onCreatePage = useNavigateToCreatePage();

  const canSeePeopleFollowerTab = useHasPermission([
    SCOPES.canSeePeopleFollowerTab,
  ]);
  const canSeePeopleFollowingTab = useHasPermission([
    SCOPES.canSeePeopleFollowingTab,
  ]);
  const { navigateToMainPageOfSection } = useNavigateToMainPageOfSection();

  const isPeople = type === 'people';
  const isPages = type === 'pages';

  const {
    data: requests,
    isLoading: isLoadingRequests,
    isError: isRequestError,
  } = useReactQuery({
    action: {
      key: [QueryKeys.followRequests],
      apiFunc: getMyFollowRequests,
      params: {
        userType: isPeople ? 'PERSON' : 'PAGE',
      },
      spreadParams: true,
    },
  });

  const {
    data: pendings,
    isLoading: isLoadingPendings,
    isError: isPendingError,
  } = useInfiniteQuery<BlockGen<PeopleType>>(
    [QueryKeys.getPendingRequests],
    {
      func: getPendingRequests,
      size: 21,
      extraProps: {
        includeMutualCounter: true,
      },
    },
    {
      enabled: isPeople,
    }
  );

  const isLoading = isLoadingPendings || isLoadingRequests;
  const hasPendingTab =
    !isPendingError && !!pendings?.length && type === 'people';
  const hasRequestTab = !isRequestError && !!requests?.content?.length;

  const tabsMappingTable = useMemo(
    () => ({
      [mainRoutes.people]: [
        {
          path: routeNames.peopleDiscover,
          title: t('discover'),
        },
        canSeePeopleFollowerTab && {
          path: routeNames.peopleFollowers,
          title: t('followers_cap'),
        },
        canSeePeopleFollowingTab && {
          path: routeNames.peopleFollowing,
          title: t('following_cap'),
        },
        canSeePeopleFollowingTab &&
          hasRequestTab && {
            path: routeNames.peopleFollowRequests,
            title: t('requests'),
            hasLeftDivider: true,
          },
        canSeePeopleFollowingTab &&
          hasPendingTab && {
            path: routeNames.peoplePendingRequests,
            title: t('pending'),
            hasLeftDivider: !hasRequestTab,
          },
        // {
        //   path: routeNames.searchCandidates,
        //   title: t('candidates'),
        //   hasLeftDivider: true,
        // },
      ].filter(Boolean),
      [mainRoutes.pages]: [
        {
          path: routeNames.pagesDiscover,
          title: t('discover'),
        },

        {
          path: routeNames.pagesFollowers,
          title: t('followers_cap'),
        },
        {
          path: routeNames.pagesFollowing,
          title: t('following_cap'),
        },
        !isBusinessApp && {
          path: routeNames.pagesYouManage,
          title: t('managing'),
        },
        hasRequestTab && {
          path: routeNames.pagesFollowRequests,
          title: t('requests'),
          hasLeftDivider: true,
        },
      ].filter(Boolean),
    }),
    [
      canSeePeopleFollowerTab,
      canSeePeopleFollowingTab,
      hasRequestTab,
      hasPendingTab,
    ]
  );

  const tabs = tabsMappingTable[path] || [];

  const title = isPeople ? t('people') : t('pages');
  const isCandidateTab = pathname.includes(routeNames.searchCandidates);

  useEffect(() => {
    document
      ?.getElementById(PROFILE_SCROLL_WRAPPER)
      ?.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  }, [pathname]);

  return (
    <JobsTabLayout
      tabs={tabs}
      linksRootClassName={classes.linksRootClassName}
      contentRootClassName={
        isCandidateTab ? classes.wideScreen : classes.fullWidthScreen
      }
      tabsRootWrapperClassName={classes.tabsRootWrapperClassName}
      actionButton={
        isPages ? (
          <Button
            leftIcon="plus"
            onClick={onCreatePage}
            label={isMoreThanTablet && t('create')}
            className={classes.createButton}
          />
        ) : isPeople && isMoreThanTablet ? (
          <Button
            leftIcon="envelope-open-text"
            leftType="far"
            label={t('invite')}
            className={classes.createBtn}
            onClick={() => openMultiStepForm({ formName: 'invitePeople' })}
          />
        ) : undefined
      }
      headerProps={{
        hideBack: false,
        visibleHeaderDivider: true,
        title,

        onTitleClick: navigateToMainPageOfSection,
        rightContent: () => (
          <ResponsiveGlobalSearch iconViewClassName={classes.searchIcon} />
        ),
      }}
      isLoading={isLoading}
    >
      {children}
    </JobsTabLayout>
  );
};

export default PeoplePagesTabLayout;
