import React from 'react';
import ResponsiveGlobalSearch from 'shared/components/molecules/ResponsiveGlobalSearch';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import { useNavigateToMainPageOfSection } from 'shared/hooks/useNavigateToMainPageOfSection';
import Button from 'shared/uikit/Button';
import useMedia from 'shared/uikit/utils/useMedia';
import { routeNames } from 'shared/utils/constants/routeNames';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './JobsTabLayout.user.component.module.scss';
import JobsTabLayout from './JobTabLayout.component';

const UserJobsTabLayout: React.FC = ({ children }: React.PropsWithChildren) => {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const { navigateToMainPageOfSection } = useNavigateToMainPageOfSection();
  const appDispatch = useGlobalDispatch();

  const onCreateJob = () => {
    appDispatch({
      type: 'TOGGLE_CREATE_JOB_MODAL_IN_USER_PROJECT',
      payload: true,
    });
  };

  const tabs = [
    {
      path: `${routeNames.discoverJobs}`,
      title: t('discover'),
    },
    {
      path: `${routeNames.appliedJobs}`,
      title: t('applied'),
    },
    {
      path: `${routeNames.savedJobs}`,
      title: t('saved'),
    },
    {
      path: `${routeNames.recentSearch}`,
      title: t('recent_search'),
    },
  ];

  return (
    <JobsTabLayout
      tabs={tabs}
      linksRootClassName={classes.linksRootClassName}
      contentRootClassName={classes.contentRootClassName}
      headerProps={{
        visibleHeaderDivider: true,
        title: t('jobs'),
        onTitleClick: navigateToMainPageOfSection,
        rightContent: () => (
          <ResponsiveGlobalSearch
            iconViewClassName={classes.globalSearchInput}
          />
        ),
      }}
      actionButton={
        isMoreThanTablet && (
          <Button
            leftIcon="plus"
            onClick={onCreateJob}
            label={t('create')}
            className={classes.createButton}
          />
        )
      }
    >
      {children}
    </JobsTabLayout>
  );
};

export default UserJobsTabLayout;
