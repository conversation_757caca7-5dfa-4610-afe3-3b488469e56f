@import '/src/shared/theme/theme.scss';

@layer global {
  :root {
    --8_12: 8px;
    --24_32: 24px;
    --16_20: 16px;
    --12_16: 12px;
    --16_8: 16px;
    --0_20: 0px;
    --0_4: 0px;
    --16_0: 16px;
    --16_12: 16px;
  }

  html {
    height: 100%;
    overflow: hidden;
  }
  body {
    height: 100%;
    overflow: hidden;
    background: colors(background);
  }

  script {
    display: none;
  }

  #root {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-y: auto;
  }

  #root {
    height: 100%;
  }

  a {
    -webkit-tap-highlight-color: transparent;
  }

  .responsive-margin-top {
    margin-top: variables(gutter) * 0.5;
  }

  .responsive-large-gutter-margin-top {
    margin-top: variables(xLargeGutter);
  }

  .half-responsive-margin-top {
    margin-top: variables(gutter) * 0.5;
  }

  b {
    font-family: fonts(bold);
  }

  .recharts-cartesian-axis-tick-line {
    stroke: colors(hoverThird);
  }

  .recharts-cartesian-axis-tick-value {
    fill: colors(primaryDisabledText);
  }
  .mr-t-8 {
    margin-top: variables(gutter) * 0.5;
  }
  .mr-t-16 {
    margin-top: variables(gutter);
  }
  .mr-t-12 {
    margin-top: variables(xLargeGutter) * 0.5;
  }
  .mr-t-24 {
    margin-top: variables(xLargeGutter);
  }
  .mr-t-20 {
    margin-top: variables(largeGutter);
  }

  .mr-l-8 {
    margin-left: variables(gutter) * 0.5;
  }

  .noDisplay {
    display: none !important;
  }

  @media (min-width: breakpoints(tablet)) {
    :root {
      --8_12: 12px;
      --24_32: 32px;
      --16_20: 20px;
      --12_16: 16px;
      --16_8: 8px;
      --0_20: 20px;
      --0_4: 4px;
      --16_0: 0px;
      --16_12: 12px;
    }
    .responsive-margin-top {
      margin-top: variables(xLargeGutter) * 0.5;
    }

    .responsive-large-gutter-margin-top {
      margin-top: variables(desktopGutter);
    }
  }
}

@supports (left: env(safe-area-inset-left)) {
  :root {
    --l-safe-area-bottom: env(safe-area-inset-bottom);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
