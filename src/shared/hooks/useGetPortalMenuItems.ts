import { SCOPES } from 'shared/constants/userRoles.scopes';
import useHasPermission from 'shared/hooks/useHasPermission';
import { mainRoutes, routeNames } from 'shared/utils/constants/routeNames';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import getPortalMenuItems from 'shared/utils/getPortalMenuItems';
import useTranslation from 'shared/utils/hooks/useTranslation';

const useGetPortalMenuItems = () => {
  const { t } = useTranslation();

  const items = [
    useHasPermission([SCOPES.canSeeHomeScreen]) && {
      id: 'home',
      rootRoute: mainRoutes.home,
      label: t('home'),
      icon: 'house',
      link: routeNames.home,
      scopes: [SCOPES.canSeeHomeScreen],
    },
    {
      id: 'discover',
      rootRoute: mainRoutes.discover,
      label: t('discover'),
      icon: 'discover',
      link: routeNames.discover,
    },
    useHasPermission([SCOPES.canSeeHomeScreen]) && {
      id: 'projects',
      rootRoute: routeNames.searchRecruiterProjects,
      label: t('projects'),
      icon: 'projects',
      link: routeNames.searchRecruiterProjects,
      scopes: [SCOPES.canSeeHomeScreen],
    },
    {
      id: 'clubs',
      rootRoute: mainRoutes.clubs,
      label: t('clubs'),
      icon: 'group',
      link: routeNames.clubs,
    },
    {
      id: 'pipelines',
      rootRoute: mainRoutes.pipelines,
      label: t('pipelines'),
      icon: 'pipeline',
      link: routeNames.pipelines,
    },
    {
      id: 'dashboard',
      rootRoute: mainRoutes.dashboard,
      label: t('dashboard'),
      icon: 'dashboard',
      link: routeNames.dashboard,
    },
    {
      id: 'news',
      rootRoute: mainRoutes.news,
      label: t('news'),
      icon: 'news',
      link: routeNames.news,
    },
    {
      id: 'articles',
      rootRoute: mainRoutes.articles,
      label: t('articles'),
      icon: 'article',
      link: routeNames.articles,
    },
    {
      id: 'candidates',
      rootRoute: routeNames.searchCandidates,
      label: t('candidates'),
      icon: 'candidates',
      link: routeNames.searchCandidates,
    },
    {
      id: 'companies',
      rootRoute: routeNames.companies.search,
      label: t('companies'),
      icon: 'companies',
      link: routeNames.companies.search,
    },
    useHasPermission([SCOPES.canSeePeopleScreen]) && {
      id: 'people',
      rootRoute: mainRoutes.people,
      label: t('people'),
      icon: 'circle-user',
      link: routeNames.peopleDiscover,
      scopes: [SCOPES.canSeePeopleScreen],
    },
    useHasPermission([SCOPES.canSeePagesScreen]) && {
      id: 'pages',
      rootRoute: mainRoutes.pages,
      label: t('pages'),
      icon: 'flag',
      link: routeNames.pagesDiscover,
      scopes: [SCOPES.canSeePagesScreen],
    },
    {
      id: 'schedules',
      rootRoute: mainRoutes.schedules,
      label: t('schedules'),
      icon: 'calendar',
      link: routeNames.schedulesCalendarMonth,
    },
    useHasPermission([SCOPES.canSeeMessagesScreen]) && {
      id: 'messages',
      rootRoute: mainRoutes.messages,
      label: t('messages'),
      icon: 'comment-alt-lines',
      link: routeNames.messages,
      scopes: [SCOPES.canSeeMessagesScreen],
    },
    {
      id: 'jobs',
      rootRoute: isBusinessApp
        ? routeNames.searchRecruiterJobs
        : mainRoutes.jobs,
      label: t('jobs'),
      icon: 'briefcase-blank',
      link: isBusinessApp
        ? routeNames.searchRecruiterJobs
        : routeNames.discoverJobs,
    },
    {
      id: 'services',
      rootRoute: routeNames.services,
      label: t('services'),
      icon: 'services',
      link: routeNames.services,
    },
    {
      id: 'ads',
      rootRoute: mainRoutes.ads,
      label: t('ads'),
      icon: 'ad',
      link: routeNames.ads,
    },
    {
      id: 'clients',
      rootRoute: mainRoutes.clients,
      label: t('clients'),
      icon: 'candidates',
      link: routeNames.clients,
    },
    {
      id: 'campaigns',
      rootRoute: mainRoutes.campaigns,
      label: t('campaigns'),
      icon: 'megaphone',
      link: routeNames.campaigns,
    },
    {
      id: 'sales',
      rootRoute: mainRoutes.sales,
      label: t('sales'),
      icon: 'sales',
      link: routeNames.sales,
    },
    {
      id: 'customers',
      rootRoute: mainRoutes.customers,
      label: t('customers'),
      icon: 'candidates',
      link: routeNames.customers,
    },
  ].filter(Boolean);

  return getPortalMenuItems(items)?.filter(Boolean);
};

export default useGetPortalMenuItems;
