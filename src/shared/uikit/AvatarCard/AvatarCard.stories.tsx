import AvatarCard from './index';
import type { AvatarCardProps } from './index';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<AvatarCardProps> = {
  title: 'MOLECULES/AvatarCard',
  component: AvatarCard,
  args: {
    data: {
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      title: '<PERSON>',
      subTitle: 'Frontend Developer',
      id: '123',
      username: 'johndo<PERSON>',
      role: 'Admin',
    },
    withPadding: true,
  },
  argTypes: {
    withPadding: { control: 'boolean' },
    noHover: { control: 'boolean' },
    automated: { control: 'boolean' },
    'data.role': { control: 'text' },
  },
};

export default meta;
type Story = StoryObj<AvatarCardProps>;

export const Default: Story = {};

export const NoImage: Story = {
  args: {
    data: {
      title: '<PERSON>',
      subTitle: 'Designer',
      id: '456',
      username: 'jane<PERSON>',
      role: 'User',
    },
  },
};

export const Automated: Story = {
  args: {
    automated: true,
  },
};

export const NoHover: Story = {
  args: {
    noHover: true,
  },
};

export const CustomAction: Story = {
  args: {
    action: <button>Invite</button>,
  },
};
