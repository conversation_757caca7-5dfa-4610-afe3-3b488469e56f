import React from 'react';
import { AutomationIcon } from '@shared/components/atoms/AutomationIcon';
import OverflowTip from 'shared/uikit/Typography/OverflowTip';
import Avatar from '../Avatar';
import BaseButton from '../Button/BaseButton';
import DividerVertical from '../Divider/DividerVertical';
import Flex from '../Flex';
import ObjectLink from '../Link/ObjectLink';
import cnj from '../utils/cnj';
import classes from './index.module.scss';
import type { AvatarProps } from '../Avatar/Avatar.component';
import type { TypographyProps } from '../Typography';
import type { JSX } from 'react';

type TextProps = Omit<TypographyProps, 'children'>;

export interface AvatarCardProps {
  data?: {
    image?: string;
    title?: ReactNode;
    subTitle?: string | React.ReactNode;
    titleHelper?: string;
    id?: string;
    username?: string;
    role?: string | (() => React.ReactElement);
  };
  avatarProps?: AvatarProps;
  titleProps?: TextProps;
  subTitleProps?: TextProps;
  action?: React.ReactNode;
  onClick?: () => void;
  containerProps?: any;
  noHover?: boolean;
  withPadding?: boolean;
  avatarClassName?: string;
  automated?: boolean;
}

const AvatarCard = ({
  data,
  avatarProps = {},
  titleProps = {},
  subTitleProps = {},
  action,
  onClick,
  containerProps = {},
  noHover,
  withPadding = true,
  avatarClassName,
  automated,
}: AvatarCardProps): JSX.Element => {
  const { image, title, titleHelper, subTitle, id, username } = data || {};

  const Wrapper = id ? ObjectLink : BaseButton;
  const wrapperProps = id ? { username, objectId: id } : { onClick };

  return (
    <Wrapper
      {...containerProps}
      {...wrapperProps}
      className={cnj(
        classes.itemContainer,
        withPadding && classes.padding,
        noHover && classes.itemContainerNoHover,
        containerProps?.className
      )}
    >
      <Avatar
        size="smd"
        imgSrc={image}
        {...avatarProps}
        className={cnj(classes.avatar, avatarClassName)}
      />
      {automated && <AutomationIcon />}
      <Flex className={classes.infoWrapper}>
        {typeof title === 'string' ? (
          <Flex flexDir="row">
            <OverflowTip
              height={19}
              color="smoke_coal"
              font="bold"
              size={16}
              {...titleProps}
            >
              {title}
            </OverflowTip>
            {data?.role && (
              <>
                <DividerVertical className={classes.divider} />
                {typeof data?.role === 'function' ? (
                  data?.role()
                ) : (
                  <OverflowTip height={16} color="border" size={14}>
                    {data.role}
                  </OverflowTip>
                )}
              </>
            )}
          </Flex>
        ) : (
          title
        )}

        {typeof subTitle === 'string' ? (
          <OverflowTip mt={4} size={14} height={16} {...subTitleProps}>
            {subTitle}
          </OverflowTip>
        ) : (
          subTitle
        )}
      </Flex>
      {action}
    </Wrapper>
  );
};

export default AvatarCard;
