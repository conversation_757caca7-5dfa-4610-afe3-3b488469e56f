import Avatar, { schema } from './Avatar.component';
import type { AvatarProps } from './Avatar.component';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<AvatarProps> = {
  title: 'ATOMS/Avatar',
  component: Avatar,
  argTypes: {
    size: {
      control: 'select',
      options: Object.keys(schema),
    },
    isOnline: { control: 'boolean' },
    showStatus: { control: 'boolean' },
    isCompany: { control: 'boolean' },
    bordered: { control: 'boolean' },
    badgeNumber: { control: 'text' },
    name: { control: 'text' },
    imgSrc: { control: 'text' },
  },
  args: {
    name: '<PERSON>',
    size: 'md',
    showStatus: true,
    isOnline: true,
    isCompany: false,
    bordered: false,
    badgeNumber: '',
    imgSrc: '',
  },
};

export default meta;
type Story = StoryObj<AvatarProps>;

export const Default: Story = {};

export const WithImage: Story = {
  args: {
    imgSrc:
      'https://storage.googleapis.com/lobox_public_images/image/small/********************************.jpeg',
  },
};

export const Company: Story = {
  args: {
    isCompany: true,
    name: 'Acme Inc.',
  },
};

export const WithBadge: Story = {
  args: {
    badgeNumber: '5',
  },
};

export const Bordered: Story = {
  args: {
    bordered: true,
  },
};

export const AllSizes: Story = {
  render: (args) => (
    <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
      {Object.keys(schema).map((size) => (
        <Avatar key={size} {...args} size={size as any} name={size} />
      ))}
    </div>
  ),
};
