const COMPANY_SERVICE = 'company-service';

const API_VERSION = '/api/v1';

const base = `${COMPANY_SERVICE}${API_VERSION}`;

const company = {
  getVendorIds: `${base}/vendors/get-current-ids`,
  addVendor: `${base}/vendor/add`,
  addVendors: `${base}/vendors/add`,
  removeVendor: (id: string) => `${base}/remove/vendor/${id}`,

  getClients: `${base}/clients/get`,
  addClient: `${base}/client/add`,
  addClients: `${base}/clients/add`,
  removeClient: (id: string) => `${base}/remove/client/${id}`,

  getSubmittedJobs: `${base}/submit/job/get-current-ids`,
  submitJob: `${base}/job/submit`,
  withdrawJob: `${base}/job/withdraw`,

  acceptRequest: (id: string) => `${base}/request/accept/${id}`,
  delcineRequest: (id: string) => `${base}/request/decline/${id}`,
  cancelRequest: (id: string) => `${base}/request/cancel/${id}`,
  getRequestedClients: `${base}/request/get-clients`,
  getRequest: `${base}/request`,
  getRequestedVendors: `${base}/request/get-vendors`,
  getPendingClients: `${base}/pending/get-clients`,
  getPendingVendors: `${base}/pending/get-vendors`,
  getPending: `${base}/pending`,
  getCompanyDetailInVendorClientByPageId: (pageId: string) =>
    `${base}/vendor-client/details/${pageId}`,
  getPendingAndRequestCount: `${base}/vendor-client/request/counter`,
  jobSubmit: `${base}/job/submit`,
  jobWithdraw: `${base}/job/withdraw`,
  searchCompany: `${base}/search-company`,
  searchCompanyFilters: `${base}/search-company/filter`,
  suggestCompany: `${base}/suggest-company`,
  submitCandidate: (vendorClientId: string) =>
    `${base}/system/submit-candidate/${vendorClientId}`,
};

export default company;
