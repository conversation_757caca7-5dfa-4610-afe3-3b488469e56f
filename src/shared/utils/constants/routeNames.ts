export const mainRoutes = {
  home: '/',
  middle: '/lb',
  feedDetail: '/feed',
  profile: '/:username',
  page: '/page',
  pages: '/pages',
  people: '/people',
  invite: '/invite',
  create: '/create',
  messages: '/messages',
  settings: '/settings',
  notifications: '/notifications',
  search: '/search',
  searchCenter: '/filter',
  explore: '/explore',
  jobs: '/jobs',
  login: '/login',
  signup: '/signup',
  welcome: '/welcome',
  verify: '/signup/verify',
  privacySecurity: '/privacy-and-help',
  privacyAndHelp: '/privacy-and-help',
  businessSolutions: '/business-solutions',
  schedules: '/schedules',
  groups: '/groups',
  profileMenu: '/profile-menu',
  switchToDesktop: '/switch-to-desktop',
  services: '/services',
  campaigns: '/campaigns',
  sales: '/sales',
  projects: '/projects',
  candidates: '/candidates',
  clients: '/clients',
  ads: '/ads',
  customers: '/customers',
  articles: '/articles',
  news: '/news',
  dashboard: '/dashboard',
  pipelines: '/pipelines',
  clubs: '/clubs',
  discover: '/discover',
  unauthorized: '/unauthorized',
};

export const routeNames = {
  home: mainRoutes.home,
  notifications: mainRoutes.notifications,
  middleScreen: {
    route: `${mainRoutes.middle}/:objectId`,
    unavailable: `${mainRoutes.middle}/unavailable`,
    makeRoute: (objectId: string): string => `${mainRoutes.middle}/${objectId}`,
  },
  feedDetail: {
    route: `${mainRoutes.feedDetail}/:feedId`,
    makeRoute: (feedId: string): string => `${mainRoutes.feedDetail}/${feedId}`,
  },
  profile: mainRoutes.profile,
  switchToDesktop: mainRoutes.switchToDesktop,
  profileMenu: mainRoutes.profileMenu,
  page: mainRoutes.page,
  pageCreation: `${mainRoutes.page}/creation`,
  feed: '/feed',
  profilePages: '/pages',
  hashtags: '/hashtags',
  collections: '/collections',
  availability: { week: '/availability/week', day: '/availability/day' },
  collection: {
    route: '/collections/:collectionId',
    makeRoute: (collectionId: string): string => `/collections/${collectionId}`,
  },
  editProfile: '/edit-profile',
  experience: '/experience',
  people: mainRoutes.people,
  peopleDiscover: `${mainRoutes.people}/discover`,
  peopleFollowers: `${mainRoutes.people}/followers`,
  peopleFollowing: `${mainRoutes.people}/following`,
  searchCandidates: `${mainRoutes.search}${mainRoutes.candidates}`,
  pages: mainRoutes.pages,
  pagesDiscover: `${mainRoutes.pages}/discover`,
  pagesYouManage: `${mainRoutes.pages}/pages-you-manage`,
  pagesFollowers: `${mainRoutes.pages}/followers`,
  pagesFollowing: `${mainRoutes.pages}/following`,
  pagesCategory: {
    main: `${mainRoutes.pages}/category`,
    route: `${mainRoutes.pages}/category/:categoryId`,
    makeRoute: (categoryId: string): string =>
      `${mainRoutes.pages}/category/${categoryId}`,
  },
  peoplePendingRequests: `${mainRoutes.people}/pending-requests`,
  peopleFollowRequests: `${mainRoutes.people}/follow-requests`,
  peopleTopSuggestions: `${mainRoutes.people}/top-suggestions`,
  pagesTopSuggestions: `${mainRoutes.pages}/top-suggestions`,
  pagesFollowRequests: `${mainRoutes.pages}/follow-requests`,
  allPeople: `${mainRoutes.people}/all-people`,
  allPages: `${mainRoutes.pages}/all-pages`,
  popularPeople: `${mainRoutes.people}/popular-people`,
  popularPages: `${mainRoutes.pages}/popular-pages`,
  invite: mainRoutes.invite,
  inviteByEmail: `${mainRoutes.invite}/by-email`,
  inviteHome: mainRoutes.invite,
  inviteDiscover: `${mainRoutes.invite}/discover`,
  inviteConnect: `${mainRoutes.invite}/connect`,
  jobs: mainRoutes.jobs,
  openJobs: `${mainRoutes.jobs}/open`,
  closedJobs: `${mainRoutes.jobs}/closed`,
  archivedJobs: `${mainRoutes.jobs}/archived`,
  unpublishedJobs: `${mainRoutes.jobs}/unpublished`,
  jobCreation: `${mainRoutes.jobs}/creation`,
  jobDetails: {
    route: `${mainRoutes.jobs}/job-details/:jobId`,
    makeRoute: (jobId: string): string =>
      `${mainRoutes.jobs}/job-details/${jobId}`,
  },
  candidate: {
    route: `${mainRoutes.jobs}/candidate/:candidateId`,
    makeRoute: (candidateId: string): string =>
      `${mainRoutes.search}${mainRoutes.candidates}?searchEntity=candidate&currentEntityId=${candidateId}`,
  },
  coming: '/coming',
  discoverJobs: `${mainRoutes.jobs}/discover`,
  appliedJobs: `${mainRoutes.jobs}/applied`,
  allAppliedJobs: `${mainRoutes.jobs}/all-applied`,
  allSavedJobs: `${mainRoutes.jobs}/all-saved`,
  savedJobs: `${mainRoutes.jobs}/saved`,
  topSuggestionJobs: `${mainRoutes.jobs}/top-suggestion`,
  popularCategories: `${mainRoutes.jobs}/popular-categories`,
  popularJobs: `${mainRoutes.jobs}/popular-jobs`,
  allJobs: `${mainRoutes.jobs}/all`,
  recentSearch: `${mainRoutes.jobs}/recent-search`,
  jobCategory: {
    main: `${mainRoutes.jobs}/category`,
    route: `${mainRoutes.jobs}/category/:categoryId`,
    makeRoute: (jobId: string): string =>
      `${mainRoutes.jobs}/category/${jobId}`,
  },
  jobsTopSuggestions: `${mainRoutes.jobs}/top-suggestions`,
  schedules: mainRoutes.schedules,
  schedulesCalendar: `${mainRoutes.schedules}/calendar`,
  schedulesCalendarYear: `${mainRoutes.schedules}/calendar/year`,
  schedulesCalendarMonth: `${mainRoutes.schedules}/calendar/month`,
  schedulesCalendarWeek: `${mainRoutes.schedules}/calendar/week`,
  schedulesCalendarDay: `${mainRoutes.schedules}/calendar/day`,
  schedulesMeetings: `${mainRoutes.schedules}/meetings`,
  schedulesEvents: `${mainRoutes.schedules}/events`,
  createSchedule: `${mainRoutes.schedules}/create`,
  schedulesReminders: `${mainRoutes.schedules}/reminders`,
  schedulesTasks: `${mainRoutes.schedules}/tasks`,
  schedulesAvailability: {
    main: `${mainRoutes.schedules}/availability`,
    route: `${mainRoutes.schedules}/availability/:id`,
    makeRoute: (id: string): string =>
      `${mainRoutes.schedules}/availability/${id}`,
  },
  schedulesAvailabilityCrEdit: {
    main: `${mainRoutes.schedules}/availability/credit`,
    route: `${mainRoutes.schedules}/availability/credit/:id`,
    makeRoute: (id: string): string =>
      `${mainRoutes.schedules}/availability/credit/${id}`,
  },
  schedulesAvailabilityDay: {
    main: `${mainRoutes.schedules}/availability/:id`,
    route: `${mainRoutes.schedules}/availability/:id/:day`,
    makeRoute: (id: string, day: string): string =>
      `${mainRoutes.schedules}/availability/${id}/${day}`,
  },
  search: mainRoutes.search,
  searchAll: `${mainRoutes.search}/all`,
  searchPeople: `${mainRoutes.search}${mainRoutes.people}`,
  searchPages: `${mainRoutes.search}${mainRoutes.pages}`,
  searchJobs: `${mainRoutes.search}${mainRoutes.jobs}`,
  searchGroups: `${mainRoutes.searchCenter}${mainRoutes.groups}`,
  searchPosts: `${mainRoutes.search}/posts`,
  searchHashtags: `${mainRoutes.search}/hashtags`,
  searchRecruiterJobs: `${mainRoutes.search}/my-jobs`,
  searchRecruiterProjects: `${mainRoutes.search}/my-projects`,
  messages: mainRoutes.messages,
  settings: mainRoutes.settings,
  settingsProfileDetail: `${mainRoutes.settings}/profile-details`,
  settingsAccount: `${mainRoutes.settings}/account`,
  settingsDeleteAccount: `${mainRoutes.settings}/account/deletion`,
  settingsSecurity: `${mainRoutes.settings}/security-login`,
  settingsContactInfo: `${mainRoutes.settings}/contact-information`,
  settingsCareerPref: `${mainRoutes.settings}/career-preferences`,
  settingsPreferences: `${mainRoutes.settings}/preferences`,
  settingsSchedulePref: `${mainRoutes.settings}/preferences/schedule`,
  settingsScheduleCalendar: `${mainRoutes.settings}/preferences/schedule/calendar`,
  settingsScheduleMeeting: `${mainRoutes.settings}/preferences/schedule/meeting`,
  settingsScheduleAvailability: `${mainRoutes.settings}/preferences/schedule/availability`,
  settingsPrivacy: `${mainRoutes.settings}/privacy`,
  settingsLocalization: `${mainRoutes.settings}/language-localization`,
  settingsFeedPref: `${mainRoutes.settings}/feed-preferences`,
  settingsHiddenPosts: `${mainRoutes.settings}/feed-preferences/hidden-posts`,
  settingsSync: `${mainRoutes.settings}/synchronization`,
  settingsNotification: {
    main: `${mainRoutes.settings}/notification-settings`,
    route: `${mainRoutes.settings}/notification-settings/:notification`,
    makeRoute: (subRouteName: string): string =>
      `${mainRoutes.settings}/notification-settings/${subRouteName}`,
  },
  settingsReviewContent: `${mainRoutes.settings}/privacy/review-content`,
  settingsEditProfileVisibility: `${mainRoutes.settings}/privacy/edit-profile-visibility`,
  settingsPageDetail: `${mainRoutes.settings}/page-details`,
  settingsPageAccount: `${mainRoutes.settings}/account`,
  settingsPageContactInfo: `${mainRoutes.settings}/contact-information`,
  settingsPagePrivacy: `${mainRoutes.settings}/privacy`,
  settingsPageFeedPref: `${mainRoutes.settings}/feed-preferences`,
  settingsPageRoles: `${mainRoutes.settings}/page-roles`,
  settingsSubscription: `${mainRoutes.settings}/subscription-pay`,
  settingsTakeBrake: `${mainRoutes.settings}/account/take-a-break`,
  settingsTemplates: `${mainRoutes.settings}/templates`,
  settingsTextTemplates: {
    main: `${mainRoutes.settings}/templates/text`,
    email: `${mainRoutes.settings}/templates/text/email`,
    message: `${mainRoutes.settings}/templates/text/message`,
    rejection: `${mainRoutes.settings}/templates/text/rejection`,
    meeting: `${mainRoutes.settings}/templates/text/meeting`,
    makeRoute: (category: any) =>
      `${mainRoutes.settings}/templates/text/${category}`,
    makeCrEditRoute: (category: any, entityId?: string) =>
      `${mainRoutes.settings}/templates/text/${category}/details${entityId ? `?entityId=${entityId}` : ''}`,
  },

  settingsQuestionTemplates: `${mainRoutes.settings}/templates/question`,
  settingsTestTemplates: `${mainRoutes.settings}/templates/test`,
  // settingsPageNotification: `${mainRoutes.settings}/notification-settings`,
  // settingsPageNotificationStatus: `${mainRoutes.settings}/notification-settings/:notification`,
  services: mainRoutes.services,
  campaigns: mainRoutes.campaigns,
  sales: mainRoutes.sales,
  projects: mainRoutes.projects,
  clients: mainRoutes.clients,
  ads: mainRoutes.ads,
  customers: mainRoutes.customers,
  articles: mainRoutes.articles,
  news: mainRoutes.news,
  dashboard: mainRoutes.dashboard,
  pipelines: mainRoutes.pipelines,
  clubs: mainRoutes.clubs,
  discover: mainRoutes.discover,
  portalProfile: '/portal',
  companies: {
    search: `${mainRoutes.search}/companies`,
    vendors: `${mainRoutes.search}/companies/vendors`,
    clients: `${mainRoutes.search}/companies/clients`,
    requests: `${mainRoutes.search}/companies/requests`,
    pending: `${mainRoutes.search}/companies/pending`,
  },
  unauthorized: mainRoutes.unauthorized,
};

export const sectionsIds = {
  whoWeAre: 'who-we-are',
  ourValues: 'our-values',
  career: 'career',
  products: 'products',
  pricing: 'pricing',
  support: 'support',
  popularTopics: 'popularTopics',
  faq: 'faq',
  feedback: 'feedback',
  termOfService: 'term-of-service',
  whoMayUseTheServices: 'who-may-use-the-services',
  contentOnTheServices: 'content-on-the-services',
  disclaimers: 'disclaimers-and-limitations-of-liability',
  general: 'general',
  basicAccountInformation: 'basic-account-information',
  publicInformation: 'public-information',
  contactInformation: 'contact-information-and-address-books',
  directMessages: 'direct-messages-and-non-public-communications',
  paymentInformation: 'payment-information',
  controlInformation: 'control-information',
  ourCookies: 'our-use-of-cookies',
  aboutSecurity: 'about-account-security',
};

export const landingRouteNames = {
  // landing routes
  prime: '/',
  findJob: '/find-job',
  followIndustry: '/follow-industry',
  careerManagement: '/career-management',
  unsubscribe: '/unsubscribe',
  explore: mainRoutes.explore,
  explorePeople: `${mainRoutes.explore}${mainRoutes.people}`,
  explorePages: `${mainRoutes.explore}${mainRoutes.pages}`,

  // login routes
  login: mainRoutes.login,
  forgetPassword: `${mainRoutes.login}/forgot-password`,
  accountNotFound: `${mainRoutes.login}/account-not-found`,
  twoFactorAuthGetCode: `${mainRoutes.login}/two-factor-authentication`,
  twoFactorAuthBackupCode: `${mainRoutes.login}/two-factor-backup-code`,
  resetPassword: `${mainRoutes.login}/reset-password/:token`,
  code: `${mainRoutes.login}/code`,

  // signup routes
  signup: mainRoutes.signup,
  getName: `${mainRoutes.signup}/get-name`,
  getCountry: `${mainRoutes.signup}/get-country`,
  getCode: `${mainRoutes.signup}/code`,
  resend: `${mainRoutes.signup}/resend`,
  review: `${mainRoutes.signup}/review`,
  secondVerify: `${mainRoutes.signup}/second-verify`,
  verify: `${mainRoutes.signup}/verify/:token`,
  welcome: mainRoutes.welcome,
  oauth2: `${mainRoutes.login}/oauth2`,

  // footer routes
  businessSolutions_products: `${mainRoutes.businessSolutions}/#${sectionsIds.products}`,
  businessSolutions_pricing: `${mainRoutes.businessSolutions}/#${sectionsIds.pricing}`,
  businessSolutions_support: `${mainRoutes.businessSolutions}/#${sectionsIds.support}`,

  // privacy and security
  privacySecurity: mainRoutes.privacySecurity,
  userAgreement: `${mainRoutes.privacySecurity}/legal/user-agreement`,
  privacy: `${mainRoutes.privacySecurity}/legal/privacy-policy`,
  cookie_policy: `${mainRoutes.privacySecurity}/legal/cookie-policy`,
  copy_right: `${mainRoutes.privacySecurity}/legal/copyright-and-intellectual-property-policy`,
  aboutCookies: `${mainRoutes.privacySecurity}/about-cookies`,
  security: `${mainRoutes.privacySecurity}/security`,
  privacyAndHelp: mainRoutes.privacyAndHelp,
  legal: `${mainRoutes.privacyAndHelp}/legal`,
  helpCenter: `${mainRoutes.privacyAndHelp}/help-center`,
  about: `${mainRoutes.privacyAndHelp}/about`,
  contactUs: `${mainRoutes.privacyAndHelp}/contact-us`,
  feedback: `${mainRoutes.privacyAndHelp}/contact-us/feedback`,
  businessSolutions: `${mainRoutes.privacyAndHelp}/contact-us/business-contact`,
};

export const needsAuthPages = [
  mainRoutes.notifications,
  mainRoutes.people,
  mainRoutes.messages,
  routeNames.middleScreen.unavailable,
  mainRoutes.settings,
  routeNames.pageCreation,
  mainRoutes.invite,
  mainRoutes.pages,
  mainRoutes.search,
  mainRoutes.feedDetail,
  mainRoutes.jobs,
  mainRoutes.searchCenter,
  mainRoutes.schedules,
  landingRouteNames.welcome,
  mainRoutes.profileMenu,
  mainRoutes.switchToDesktop,
  routeNames.companies.search,
  routeNames.companies.clients,
  routeNames.companies.vendors,
  routeNames.companies.pending,
  routeNames.companies.requests,
  mainRoutes.candidates,
  mainRoutes.projects,
  routeNames.searchRecruiterJobs,
  routeNames.searchRecruiterProjects,
];

export const forbiddenForAuthUsersPages = [
  landingRouteNames.findJob,
  landingRouteNames.followIndustry,
  landingRouteNames.careerManagement,
  landingRouteNames.explore,
  landingRouteNames.explorePeople,
  landingRouteNames.explorePages,
  landingRouteNames.login,
  landingRouteNames.forgetPassword,
  landingRouteNames.twoFactorAuthGetCode,
  landingRouteNames.twoFactorAuthBackupCode,
  landingRouteNames.resetPassword,
  landingRouteNames.code,
  landingRouteNames.signup,
  landingRouteNames.getName,
  landingRouteNames.getCountry,
  landingRouteNames.getCode,
  landingRouteNames.resend,
  landingRouteNames.review,
  landingRouteNames.secondVerify,
  landingRouteNames.verify,
  landingRouteNames.welcome,
  landingRouteNames.oauth2,
  routeNames.unauthorized,
];

export const doesNotNeedAuthPages = [
  routeNames.searchJobs,
  routeNames.searchPosts,
  routeNames.schedulesAvailability.main,
  routeNames.schedulesCalendar,
  routeNames.schedulesCalendarYear,
  routeNames.schedulesCalendarMonth,
  routeNames.schedulesCalendarWeek,
  routeNames.schedulesCalendarDay,
];

export const defaultRoutesOfSections = {
  jobs: {
    user: routeNames.discoverJobs,
    editor: routeNames.openJobs,
  },
  people: routeNames.peopleDiscover,
  pages: routeNames.pagesDiscover,
};
